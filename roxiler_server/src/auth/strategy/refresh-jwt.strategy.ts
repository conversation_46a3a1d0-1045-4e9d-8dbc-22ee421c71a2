import { Injectable, UnauthorizedException } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { PassportStrategy } from "@nestjs/passport";
import { ExtractJwt, Strategy } from "passport-jwt";
import { RedisService } from "src/redis/redis.service";

@Injectable()
export class RefreshJwtStrategy extends PassportStrategy(Strategy, 'jwt-refresh') {
    constructor(
        private configService: ConfigService,
        private redisService: RedisService
    ) {
        const jwtRefreshSecret = configService.get<string>('JWT_REFRESH_SECRET') || '';
        super({
            jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
            ignoreExpiration: false,
            secretOrKey: jwtRefreshSecret,
            passReqToCallback: true,
        });
    }

    async validate(req: any, payload: any) {
        const authHeader = req.headers['authorization'];
        if (!authHeader) {
            throw new UnauthorizedException('No authorization header');
        }

        const refreshToken = authHeader.replace('Bearer ', '').trim();
        const userId = payload.sub;

        // Validate refresh token exists in Redis
        const isValidToken = await this.redisService.isRefreshTokenValid(userId, refreshToken);
        if (!isValidToken) {
            throw new UnauthorizedException('Invalid refresh token');
        }

        return { ...payload, refreshToken };
    }
}