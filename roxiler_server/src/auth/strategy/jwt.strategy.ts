import { Injectable, UnauthorizedException } from "@nestjs/common";
import { PassportStrategy } from '@nestjs/passport';
import { Strategy, ExtractJwt } from 'passport-jwt'
import { ConfigService } from "@nestjs/config";
import { RedisService } from "src/redis/redis.service";

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
    constructor(
        private configService: ConfigService,
        private redisService: RedisService
    ) {
        const jwtSecret = configService.get<string>('JWT_SECRET') || '';
        super({
            jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
            ignoreExpiration: false,
            secretOrKey: jwtSecret,
            passReqToCallback: true,
        });
    }

    async validate(req: any, payload: any) {
        const authHeader = req.headers['authorization'];
        if (authHeader) {
            const token = authHeader.replace('Bearer ', '').trim();

            // Check if token is blacklisted
            const isBlacklisted = await this.redisService.isTokenBlacklisted(token);
            if (isBlacklisted) {
                throw new UnauthorizedException('Token has been invalidated');
            }
        }

        return {
            userId: payload.sub,
            email: payload.email,
            role: payload.role
        };
    }
}