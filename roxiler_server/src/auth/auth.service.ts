import { ConflictException, Injectable, UnauthorizedException } from "@nestjs/common"
import { UserService } from "src/users/users.service"
import { JwtService } from "@nestjs/jwt"
import { ConfigService } from "@nestjs/config"
import { RedisService } from "src/redis/redis.service"
import * as  bcrypt from "bcrypt"
import { Users } from "src/users/entities/User.entity"
import { CreateUserDto } from "src/users/dto/createUser.dto"
import { LoginDto } from "./dto/login.dto"

@Injectable()
export class AuthService{
 constructor(
   private userService: UserService,
   private jwtService: JwtService,
   private configService: ConfigService,
   private redisService: RedisService
 ) {}

 async ValidateUser(email:string,password:string){
    const user= await this.userService.findOne(email);
    if(user && (await bcrypt.compare(password,user.password))){
        const {password,...result}=user;
        return result;
    }
    return null;

 }

async refreshToken(userId: number, refreshToken: string) {
  // Verify the refresh token exists in Redis
  const isValidToken = await this.redisService.isRefreshTokenValid(userId, refreshToken);
  if (!isValidToken) {
    throw new UnauthorizedException('Invalid refresh token');
  }

  // Get user details
  const user = await this.userService.findById(userId);
  if (!user) {
    throw new UnauthorizedException('User not found');
  }

  // Generate new access token
  const payload = { email: user.email, sub: user.user_id, role: user.role };
  const accessToken = this.jwtService.sign(payload, {
    secret: this.configService.get<string>('JWT_SECRET'),
    expiresIn: '15m',
  });

  // Generate new refresh token
  const newRefreshToken = this.jwtService.sign(payload, {
    secret: this.configService.get<string>('JWT_REFRESH_SECRET'),
    expiresIn: '7d',
  });

  // Store new refresh token in Redis (7 days)
  await this.redisService.storeRefreshToken(userId, newRefreshToken, 7 * 24 * 60 * 60);

  return {
    access_token: accessToken,
    refresh_token: newRefreshToken,
    user: {
      id: user.user_id,
      email: user.email,
      name: user.name,
      role: user.role
    }
  };
}

 async register(createuserdto: CreateUserDto) {
    const existing_user = await this.userService.findOne(createuserdto.email)

    if (existing_user) {
        throw new ConflictException("User already exists")
    }
    const user = await this.userService.CreateUser(createuserdto);

    const payload = { email: user.email, sub: user.user_id, role: user.role }

    const accessToken = this.jwtService.sign(payload, {
      secret: this.configService.get<string>('JWT_SECRET'),
      expiresIn: '15m',
    });

    const refreshToken = this.jwtService.sign(payload, {
      secret: this.configService.get<string>('JWT_REFRESH_SECRET'),
      expiresIn: '7d',
    });

    // Store refresh token in Redis (7 days)
    await this.redisService.storeRefreshToken(user.user_id, refreshToken, 7 * 24 * 60 * 60);

    return {
      access_token: accessToken,
      refresh_token: refreshToken,
      user: {
        id: user.user_id,
        email: user.email,
        name: user.name,
        role: user.role
      }
    };
 }

async login(loginDto: LoginDto) {
  const user = await this.ValidateUser(loginDto.email, loginDto.password);

  if (!user) {
    throw new UnauthorizedException("Invalid email or password");
  }

  const payload = { sub: user.user_id, email: user.email, role: user.role };

  const accessToken = this.jwtService.sign(payload, {
    secret: this.configService.get<string>('JWT_SECRET'),
    expiresIn: '15m',
  });

  const refreshToken = this.jwtService.sign(payload, {
    secret: this.configService.get<string>('JWT_REFRESH_SECRET'),
    expiresIn: '7d',
  });

  // Store refresh token in Redis (7 days)
  await this.redisService.storeRefreshToken(user.user_id, refreshToken, 7 * 24 * 60 * 60);

  return {
    access_token: accessToken,
    refresh_token: refreshToken,
    user: {
      id: user.user_id,
      email: user.email,
      name: user.name,
      role: user.role
    }
  };
}

async logout(userId: number, accessToken?: string): Promise<void> {
  // Remove refresh token from Redis
  await this.redisService.deleteRefreshToken(userId);

  // Optionally blacklist the current access token
  if (accessToken) {
    // Extract token without 'Bearer ' prefix
    const token = accessToken.replace('Bearer ', '');
    // Blacklist for remaining token lifetime (15 minutes)
    await this.redisService.blacklistToken(token, 15 * 60);
  }

  // Remove user session
  await this.redisService.deleteUserSession(userId);
}

async validateRefreshToken(userId: number, refreshToken: string): Promise<boolean> {
  return await this.redisService.isRefreshTokenValid(userId, refreshToken);
}

async isTokenBlacklisted(token: string): Promise<boolean> {
  return await this.redisService.isTokenBlacklisted(token);
}


}
