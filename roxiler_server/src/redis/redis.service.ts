import { Injectable, OnM<PERSON><PERSON><PERSON><PERSON>roy, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { createClient, RedisClientType } from 'redis';

@Injectable()
export class RedisService implements OnModuleInit, OnModuleDestroy {
  private client: RedisClientType;

  constructor(private configService: ConfigService) {}

  async onModuleInit() {
    this.client = createClient({
      url: this.configService.get<string>('REDIS_URL') || 'redis://localhost:6379',
    });

    this.client.on('error', (err) => {
      console.error('Redis Client Error:', err);
    });

    this.client.on('connect', () => {
      console.log('Connected to Redis');
    });

    await this.client.connect();
  }

  async onModuleDestroy() {
    if (this.client) {
      await this.client.disconnect();
    }
  }

  // Refresh token operations
  async storeRefreshToken(userId: number, refreshToken: string, expiresIn: number = 7 * 24 * 60 * 60): Promise<void> {
    const key = `refresh_token:${userId}`;
    await this.client.setEx(key, expiresIn, refreshToken);
  }

  async getRefreshToken(userId: number): Promise<string | null> {
    const key = `refresh_token:${userId}`;
    return await this.client.get(key);
  }

  async deleteRefreshToken(userId: number): Promise<void> {
    const key = `refresh_token:${userId}`;
    await this.client.del(key);
  }

  async isRefreshTokenValid(userId: number, refreshToken: string): Promise<boolean> {
    const storedToken = await this.getRefreshToken(userId);
    return storedToken === refreshToken;
  }

  // Session management
  async storeUserSession(userId: number, sessionData: any, expiresIn: number = 24 * 60 * 60): Promise<void> {
    const key = `session:${userId}`;
    await this.client.setEx(key, expiresIn, JSON.stringify(sessionData));
  }

  async getUserSession(userId: number): Promise<any | null> {
    const key = `session:${userId}`;
    const sessionData = await this.client.get(key);
    return sessionData ? JSON.parse(sessionData) : null;
  }

  async deleteUserSession(userId: number): Promise<void> {
    const key = `session:${userId}`;
    await this.client.del(key);
  }

  // Blacklist tokens (for logout)
  async blacklistToken(token: string, expiresIn: number): Promise<void> {
    const key = `blacklist:${token}`;
    await this.client.setEx(key, expiresIn, 'blacklisted');
  }

  async isTokenBlacklisted(token: string): Promise<boolean> {
    const key = `blacklist:${token}`;
    const result = await this.client.get(key);
    return result === 'blacklisted';
  }

  // Generic Redis operations
  async set(key: string, value: string, expiresIn?: number): Promise<void> {
    if (expiresIn) {
      await this.client.setEx(key, expiresIn, value);
    } else {
      await this.client.set(key, value);
    }
  }

  async get(key: string): Promise<string | null> {
    return await this.client.get(key);
  }

  async del(key: string): Promise<void> {
    await this.client.del(key);
  }

  async exists(key: string): Promise<boolean> {
    const result = await this.client.exists(key);
    return result === 1;
  }

  async expire(key: string, seconds: number): Promise<void> {
    await this.client.expire(key, seconds);
  }

  async ttl(key: string): Promise<number> {
    return await this.client.ttl(key);
  }
}
