# Redis Setup for Refresh Token Management

This application now uses Redis to store and manage refresh tokens for enhanced security.

## Features Added

### 1. Redis Integration
- **Redis Service**: Handles all Redis operations including token storage, validation, and cleanup
- **Global Redis Module**: Available throughout the application
- **Connection Management**: Automatic connection and disconnection handling

### 2. Refresh Token Management
- **Secure Storage**: Refresh tokens are stored in Redis with expiration (7 days)
- **Token Validation**: All refresh tokens are validated against Redis before use
- **Token Rotation**: New refresh tokens are generated on each refresh request
- **Automatic Cleanup**: Expired tokens are automatically removed by Redis

### 3. Enhanced Security Features
- **Token Blacklisting**: Access tokens can be blacklisted on logout
- **Session Management**: User sessions are tracked in Redis
- **Token Invalidation**: Logout invalidates all user tokens immediately

### 4. API Endpoints
- `POST /auth/login` - Login with refresh token storage
- `POST /auth/register` - Register with refresh token storage
- `POST /auth/refresh` - Refresh access token using refresh token
- `POST /auth/logout` - Logout and invalidate all tokens

## Prerequisites

### Install Redis
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install redis-server

# macOS
brew install redis

# Windows (using WSL or Docker)
docker run -d -p 6379:6379 redis:alpine
```

### Start Redis
```bash
# Linux/macOS
redis-server

# Or as a service
sudo systemctl start redis-server

# Docker
docker run -d -p 6379:6379 --name redis redis:alpine
```

## Environment Variables

Add these to your `.env` file:

```env
# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
```

## Usage

### Server-side (NestJS)
The Redis integration is automatic. The auth service now:
1. Stores refresh tokens in Redis on login/register
2. Validates refresh tokens against Redis
3. Rotates tokens on refresh
4. Cleans up tokens on logout

### Client-side (React)
The client now:
1. Stores both access and refresh tokens
2. Automatically refreshes expired access tokens
3. Handles token rotation seamlessly
4. Clears all tokens on logout

## Redis Key Structure

- `refresh_token:{userId}` - Stores refresh tokens
- `session:{userId}` - Stores user session data
- `blacklist:{token}` - Stores blacklisted access tokens

## Security Benefits

1. **Token Revocation**: Tokens can be immediately invalidated
2. **Session Control**: Track and manage user sessions
3. **Automatic Cleanup**: Expired tokens are automatically removed
4. **Centralized Storage**: All token state is managed centrally
5. **Scalability**: Redis can be clustered for high availability

## Testing

1. Start Redis server
2. Start the NestJS application
3. Login to get access and refresh tokens
4. Test token refresh by waiting for access token expiration
5. Test logout to verify token invalidation

## Troubleshooting

### Redis Connection Issues
- Ensure Redis is running: `redis-cli ping` should return `PONG`
- Check Redis logs: `sudo journalctl -u redis-server`
- Verify connection string in `.env`

### Token Issues
- Check Redis keys: `redis-cli keys "*"`
- Monitor Redis operations: `redis-cli monitor`
- Verify JWT secrets are set correctly
