import { Controller, UseGuards, Request } from "@nestjs/common";
import { AuthService } from "./auth.service";
import { RegisterDto } from "./dto/register.dto";
import { Get, Post, Body, Headers } from "@nestjs/common";
import { Public } from "./decoraters/public.decorator";
import { LoginDto } from "./dto/login.dto";
import { RefreshTokenGuard } from "./guards/refresh-token.guard";

@Controller('auth')
export class AuthController{
    constructor(private  authService: AuthService){}

    @Public()
    @Post('register')
    async register(@Body() registerDto:RegisterDto){
        return this.authService.register(registerDto)
    }


    @Public()
    @Post('login')
    async login(@Body() loginDto: LoginDto) {
        return this.authService.login(loginDto)
    }

    @Public()
    @UseGuards(RefreshTokenGuard)
    @Post('refresh')
    async refreshToken(@Request() req: any) {
        const userId = req.user.sub;
        const refreshToken = req.user.refreshToken;
        return this.authService.refreshToken(userId, refreshToken);
    }

    @Post('logout')
    async logout(@Request() req: any, @Headers('authorization') authHeader?: string) {
        const userId = req.user.sub;
        return this.authService.logout(userId, authHeader);
    }


}